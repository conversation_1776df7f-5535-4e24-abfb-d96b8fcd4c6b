# Windows Configuration for Banana Bun
# Copy this to .env and adjust paths as needed

# Base paths - adjust to your preferred location
# These use Windows-style paths but forward slashes work too
BASE_PATH=%USERPROFILE%/Documents/BananaBun
MEDIA_COLLECTION_PATH=%USERPROFILE%/Documents/Media

# OpenAI Configuration
OPENAI_API_KEY=

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:8b
OLLAMA_FAST_MODEL=llama3.2:3b

# ChromaDB Configuration
CHROMA_URL=http://localhost:8000
CHROMA_TENANT=default_tenant

# Meilisearch Configuration
MEILISEARCH_URL=http://localhost:7700
MEILISEARCH_MASTER_KEY=
MEILISEARCH_INDEX_NAME=media_index

# Whisper Configuration
WHISPER_MODEL=turbo
WHISPER_DEVICE=cpu
WHISPER_LANGUAGE=auto
WHISPER_CHUNK_DURATION=30

# Vision/CLIP Configuration
VISION_MODEL=openai/clip-vit-base-patch32
FRAME_INTERVAL_SECONDS=10
MAX_FRAMES_PER_VIDEO=50
ENABLE_SCENE_DETECTION=false

# AWS S3 Configuration (adjust if using S3)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=
S3_DEFAULT_BUCKET=
S3_DEFAULT_DOWNLOAD_PATH=%USERPROFILE%/Documents/s3-downloads
S3_SYNC_LOG_PATH=%USERPROFILE%/Documents/BananaBun/logs/s3_sync

# Media Collection Paths - adjust to your media directories
# You can use drive letters like D:/ or E:/ if you have dedicated media drives
MEDIA_COLLECTION_TV=%USERPROFILE%/Documents/Media/TV Shows
MEDIA_COLLECTION_MOVIES=%USERPROFILE%/Documents/Media/Movies
MEDIA_COLLECTION_YOUTUBE=%USERPROFILE%/Documents/Media/YouTube
MEDIA_COLLECTION_CATCHALL=%USERPROFILE%/Documents/Media/Downloads

# Media Tool Paths (should work if installed via package managers)
FFPROBE_PATH=ffprobe
MEDIAINFO_PATH=mediainfo

# yt-dlp Configuration
YTDLP_PATH=yt-dlp
YTDLP_DEFAULT_FORMAT=best[height<=1080]
YTDLP_DEFAULT_QUALITY=720p
YTDLP_OUTPUT_TEMPLATE=%(title)s [%(id)s].%(ext)s

# RSS Configuration
RSS_ENABLED=true
RSS_CHECK_INTERVAL=3600
RSS_FEEDS=https://example.com/podcast.xml,https://example.com/video-feed.xml

# Windows-specific notes:
# - Use %USERPROFILE% for user home directory
# - Forward slashes (/) work in most cases and are more portable
# - If you have dedicated drives for media (D:/, E:/, etc.), update the MEDIA_COLLECTION_* paths
# - Install tools via package managers when possible:
#   - Chocolatey: choco install ffmpeg yt-dlp
#   - Scoop: scoop install ffmpeg yt-dlp
#   - winget: winget install FFmpeg yt-dlp
