{"name": "banana-bun", "version": "1.0.0", "description": "A bun-sized AI assistant for organizing your digital life with local AI models", "module": "src/index.ts", "type": "module", "private": false, "keywords": ["ai", "media", "organization", "bun", "privacy", "local-ai", "transcription", "search"], "author": "Your Name", "license": "MIT", "scripts": {"start": "bun run src/index.ts", "dev": "bun --watch src/index.ts", "dev:with-services": "bun run dev:check-services && bun --watch src/index.ts", "dev:services": "powershell -ExecutionPolicy Bypass -File ./scripts/windows/start-services-windows.ps1", "dev:services:stop": "powershell -ExecutionPolicy Bypass -File ./scripts/windows/stop-services-windows.ps1", "dev:check-services": "bun run src/utils/check-and-start-services.ts", "setup": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd', ['/c', 'setup.bat'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['setup.sh'], {stdio: 'inherit'})\"", "services:start": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd', ['/c', 'start-services.bat'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['start-services.sh'], {stdio: 'inherit'})\"", "services:stop": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd', ['/c', 'stop-services.bat'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['stop-services.sh'], {stdio: 'inherit'})\"", "test": "bun test", "test:watch": "bun test --watch", "test:report": "bun test --coverage --coverage-reporter=lcov --preload ./test-setup.ts | tee test-results.txt", "test:mcp": "bun run test-mcp-integration.ts", "test:new-mcp": "bun test test/metadata-optimization-server.test.ts test/pattern-analysis-server.test.ts test/resource-optimization-server.test.ts test/content-quality-server.test.ts test/user-behavior-server.test.ts test/new-mcp-servers-integration.test.ts", "test:metadata": "bun test test/metadata-optimization-server.test.ts", "test:patterns": "bun test test/pattern-analysis-server.test.ts", "test:resources": "bun test test/resource-optimization-server.test.ts", "test:quality": "bun test test/content-quality-server.test.ts", "test:behavior": "bun test test/user-behavior-server.test.ts", "test:integration": "bun test test/new-mcp-servers-integration.test.ts", "mcp:chromadb": "bun run src/mcp/chromadb-server.ts", "mcp:monitor": "bun run src/mcp/monitor-server.ts", "mcp:meilisearch": "bun run src/mcp/meilisearch-server.ts", "mcp:whisper": "bun run src/mcp/whisper-server.ts", "mcp:intelligence": "bun run src/mcp/media-intelligence-server.ts", "mcp:planning": "bun run src/mcp/llm-planning-server.ts", "mcp:metadata": "bun run src/mcp/metadata-optimization-server.ts", "mcp:patterns": "bun run src/mcp/pattern-analysis-server.ts", "mcp:resources": "bun run src/mcp/resource-optimization-server.ts", "mcp:quality": "bun run src/mcp/content-quality-server.ts", "mcp:behavior": "bun run src/mcp/user-behavior-server.ts", "lint-task": "bun run src/cli/lint-task.ts", "migrate": "bun run src/migrations/migrate-all.ts", "schedule": "bun run src/cli/schedule-manager.ts", "media-ingest": "bun run src/cli/media-ingest.ts", "media-organize": "bun run src/cli/organize-media.ts", "media-search": "bun run src/cli/media-search.ts", "smart-search": "bun run src/cli/smart-media-search.ts", "smart-transcribe": "bun run src/cli/smart-transcribe.ts", "media-intelligence": "bun run src/cli/media-intelligence.ts", "media-tags": "bun run src/cli/media-tags.ts", "banana-summarize": "bun run src/cli/banana-summarize.ts", "banana-recommend": "bun run src/cli/banana-recommend.ts", "banana-detect-scenes": "bun run src/cli/banana-detect-scenes.ts", "banana-detect-objects": "bun run src/cli/banana-detect-objects.ts", "banana-audio-analyze": "bun run src/cli/banana-audio-analyze.ts", "banana-embed-media": "bun run src/cli/banana-embed-media.ts", "banana-search-similar": "bun run src/cli/banana-search-similar.ts", "analyze-task-metrics": "bun run src/cli/analyze-task-metrics.ts", "run-feedback-loop": "bun run src/cli/run-feedback-loop.ts", "generate-optimized-plan": "bun run src/cli/generate-optimized-plan.ts", "analyze-system-performance": "bun run src/cli/analyze-system-performance.ts", "optimize-metadata": "bun run src/cli/optimize-metadata.ts", "manage-plan-templates": "bun run src/cli/manage-plan-templates.ts", "test-llm-planning": "bun run src/cli/test-llm-planning.ts", "analyze-activity-patterns": "bun run src/cli/analyze-activity-patterns.ts", "view-detected-patterns": "bun run src/cli/view-detected-patterns.ts", "generate-scheduling-rules": "bun run src/cli/generate-scheduling-rules.ts", "optimize-resource-schedule": "bun run src/cli/optimize-resource-schedule.ts", "search-similar-patterns": "bun run src/cli/search-similar-patterns.ts", "analyze-feedback-enhanced": "bun run src/cli/analyze-feedback-enhanced.ts", "test-tag-strategies": "bun run src/cli/test-tag-strategies.ts", "analyze-cross-modal-intelligence": "bun run src/cli/analyze-cross-modal-intelligence.ts"}, "devDependencies": {"@types/bun": "latest", "@types/ws": "^8.5.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@chroma-core/default-embed": "^0.1.8", "@modelcontextprotocol/sdk": "^0.5.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/node": "24.0.1", "better-sqlite3": "^11.10.0", "chromadb": "^3.0.1", "meilisearch": "0.51.0", "music-metadata": "^11.2.3", "node-ffmpeg-stream": "^1.1.0", "openai": "^5.3.0", "ws": "^8.18.0", "yaml": "^2.8.0"}}