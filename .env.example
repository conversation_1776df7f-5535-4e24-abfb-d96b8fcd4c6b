# OpenAI Configuration
OPENAI_API_KEY=

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=qwen3:8b
OLLAMA_FAST_MODEL=llama3.2:3b

# ChromaDB Configuration
CHROMA_URL=http://localhost:8000
CHROMA_TENANT=default_tenant

# Meilisearch Configuration
MEILISEARCH_URL=http://localhost:7700
MEILISEARCH_MASTER_KEY=
MEILISEARCH_INDEX_NAME=media_index

# Whisper Configuration
WHISPER_MODEL=turbo
WHISPER_DEVICE=cpu
WHISPER_LANGUAGE=auto
WHISPER_CHUNK_DURATION=30

# Vision/CLIP Configuration
VISION_MODEL=openai/clip-vit-base-patch32
FRAME_INTERVAL_SECONDS=10
MAX_FRAMES_PER_VIDEO=50
ENABLE_SCENE_DETECTION=false

# Base Paths (optional - will use platform-appropriate defaults if not set)
# BASE_PATH=/path/to/banana-bun-data
# MEDIA_COLLECTION_PATH=/path/to/media

# AWS S3 Configuration (optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
S3_ENDPOINT=
S3_DEFAULT_BUCKET=
# S3_DEFAULT_DOWNLOAD_PATH=/path/to/s3-downloads
# S3_SYNC_LOG_PATH=/path/to/banana-bun-data/logs/s3_sync

# Media Collection Paths (optional - will use defaults under MEDIA_COLLECTION_PATH if not set)
# MEDIA_COLLECTION_TV=/path/to/media/TV Shows
# MEDIA_COLLECTION_MOVIES=/path/to/media/Movies
# MEDIA_COLLECTION_YOUTUBE=/path/to/media/YouTube
# MEDIA_COLLECTION_CATCHALL=/path/to/media/Downloads

# Media Tool Paths
FFPROBE_PATH=ffprobe
MEDIAINFO_PATH=mediainfo

# ===== CONTENT DISCOVERY AND DOWNLOADER INTEGRATION =====

# yt-dlp Configuration
YTDLP_PATH=yt-dlp
YTDLP_DEFAULT_FORMAT=best[height<=1080]
YTDLP_DEFAULT_QUALITY=720p
YTDLP_OUTPUT_TEMPLATE=%(title)s [%(id)s].%(ext)s

# RSS Configuration
RSS_ENABLED=true
RSS_CHECK_INTERVAL=3600
RSS_FEEDS=https://example.com/podcast.xml,https://example.com/video-feed.xml
