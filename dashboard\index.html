<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto py-8">
        <h1 class="text-2xl font-bold mb-4">🎯 Task Dashboard - Phase 3 Enhanced</h1>
        
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-2">Inbox (incoming/)</h2>
            <ul class="list-disc ml-6">
                <li class="text-green-600">Inbox is empty! 🎉</li>
            </ul>
        </div>
    
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">📊 Review Metrics</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">0</div>
                        <div class="text-sm text-gray-600">Passed Reviews</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">0</div>
                        <div class="text-sm text-gray-600">Failed Reviews</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">0.0</div>
                        <div class="text-sm text-gray-600">Avg Score</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">0</div>
                        <div class="text-sm text-gray-600">Total Reviews</div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm font-semibold mb-2">Score Distribution:</div>
                    <div class="flex space-x-2">
                        <span class="px-2 py-1 text-xs bg-green-500 text-white rounded">Excellent: 0</span>
                        <span class="px-2 py-1 text-xs bg-blue-500 text-white rounded">Good: 0</span>
                        <span class="px-2 py-1 text-xs bg-yellow-500 text-white rounded">Fair: 0</span>
                        <span class="px-2 py-1 text-xs bg-red-500 text-white rounded">Poor: 0</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">🧠 Planner Metrics</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">0</div>
                        <div class="text-sm text-gray-600">Total Plans</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">0.0</div>
                        <div class="text-sm text-gray-600">Avg Subtasks</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">0%</div>
                        <div class="text-sm text-gray-600">Success w/ Context</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">0%</div>
                        <div class="text-sm text-gray-600">Success w/o Context</div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm font-semibold mb-2">Common Patterns:</div>
                    <div class="flex flex-wrap gap-1">
                        
                    </div>
                </div>
            </div>
        </div>
    
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold">📋 Task List</h2>
            </div>
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-2 py-1 text-xs text-left">ID</th>
                        <th class="px-2 py-1 text-xs text-left">Type</th>
                        <th class="px-2 py-1 text-xs text-left">Description</th>
                        <th class="px-2 py-1 text-xs text-left">Status</th>
                        <th class="px-2 py-1 text-xs text-left">Review</th>
                        <th class="px-2 py-1 text-xs text-left">Score</th>
                        <th class="px-2 py-1 text-xs text-left">Context</th>
                        <th class="px-2 py-1 text-xs text-left">Created</th>
                        <th class="px-2 py-1 text-xs text-left">Started</th>
                        <th class="px-2 py-1 text-xs text-left">Finished</th>
                        <th class="px-2 py-1 text-xs text-left">Error</th>
                    </tr>
                </thead>
                <tbody>
                    
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/29'">
            <td class="px-2 py-1 text-xs">29</td>
            <td class="px-2 py-1 text-xs">batch</td>
            <td class="px-2 py-1 text-xs">Memory-safe download of all photos split by device and year to prevent memory issues</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:41:56</td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:41:58</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/30'">
            <td class="px-2 py-1 text-xs">30</td>
            <td class="px-2 py-1 text-xs">batch</td>
            <td class="px-2 py-1 text-xs">Memory-safe download of all photos split by device and year to prevent memory issues</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:41:56</td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:42:03</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/27'">
            <td class="px-2 py-1 text-xs">27</td>
            <td class="px-2 py-1 text-xs">batch</td>
            <td class="px-2 py-1 text-xs">Memory-safe download of all photos split by device and year to prevent memory issues</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:35:48</td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:35:52</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/28'">
            <td class="px-2 py-1 text-xs">28</td>
            <td class="px-2 py-1 text-xs">batch</td>
            <td class="px-2 py-1 text-xs">Memory-safe download of all photos split by device and year to prevent memory issues</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:35:48</td>
            <td class="px-2 py-1 text-xs">2025-06-10 03:35:57</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/22'">
            <td class="px-2 py-1 text-xs">22</td>
            <td class="px-2 py-1 text-xs">llm</td>
            <td class="px-2 py-1 text-xs">Generate summary of research paper</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08T19:27:52.662Z</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/23'">
            <td class="px-2 py-1 text-xs">23</td>
            <td class="px-2 py-1 text-xs">tool</td>
            <td class="px-2 py-1 text-xs">Process video file and extract audio</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08T19:27:52.667Z</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/24'">
            <td class="px-2 py-1 text-xs">24</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Run data analysis script</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-red-100 text-red-800">failed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/25'">
            <td class="px-2 py-1 text-xs">25</td>
            <td class="px-2 py-1 text-xs">llm</td>
            <td class="px-2 py-1 text-xs">Translate document to Spanish</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08T19:27:52.676Z</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/26'">
            <td class="px-2 py-1 text-xs">26</td>
            <td class="px-2 py-1 text-xs">batch</td>
            <td class="px-2 py-1 text-xs">Process multiple images</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs">2025-06-08 19:27:52</td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/20'">
            <td class="px-2 py-1 text-xs">20</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:14</td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:15</td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:07:23</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/21'">
            <td class="px-2 py-1 text-xs">21</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:14</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:10:30</td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:07:58</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/19'">
            <td class="px-2 py-1 text-xs">19</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:13</td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:15</td>
            <td class="px-2 py-1 text-xs">2025-05-27 16:06:15</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/17'">
            <td class="px-2 py-1 text-xs">17</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:38</td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:41</td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:47</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/18'">
            <td class="px-2 py-1 text-xs">18</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:38</td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:47</td>
            <td class="px-2 py-1 text-xs">2025-05-27 15:25:51</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/14'">
            <td class="px-2 py-1 text-xs">14</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:33:37</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:33:40</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:34:01</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/15'">
            <td class="px-2 py-1 text-xs">15</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:33:37</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:34:01</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:34:03</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/16'">
            <td class="px-2 py-1 text-xs">16</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:33:37</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:34:03</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:34:04</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/11'">
            <td class="px-2 py-1 text-xs">11</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:35</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:10:15</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:40</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/12'">
            <td class="px-2 py-1 text-xs">12</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:35</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:10:20</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:41</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/13'">
            <td class="px-2 py-1 text-xs">13</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:35</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:10:25</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:42</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/10'">
            <td class="px-2 py-1 text-xs">10</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:34</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:39</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:22:39</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/8'">
            <td class="px-2 py-1 text-xs">8</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:20</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:23</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:24</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/9'">
            <td class="px-2 py-1 text-xs">9</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:20</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:24</td>
            <td class="px-2 py-1 text-xs">2025-05-27 14:07:24</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/6'">
            <td class="px-2 py-1 text-xs">6</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:11</td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:12</td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:12</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/7'">
            <td class="px-2 py-1 text-xs">7</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:11</td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:12</td>
            <td class="px-2 py-1 text-xs">2025-05-27 13:06:12</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/5'">
            <td class="px-2 py-1 text-xs">5</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-27 10:18:33</td>
            <td class="px-2 py-1 text-xs">2025-05-27 10:18:35</td>
            <td class="px-2 py-1 text-xs">2025-05-27 10:18:36</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/4'">
            <td class="px-2 py-1 text-xs">4</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-25 19:41:22</td>
            <td class="px-2 py-1 text-xs">2025-05-25 19:41:27</td>
            <td class="px-2 py-1 text-xs">2025-05-25 19:41:27</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/3'">
            <td class="px-2 py-1 text-xs">3</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-green-100 text-green-800">completed</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-25 17:16:32</td>
            <td class="px-2 py-1 text-xs">2025-05-25 17:16:32</td>
            <td class="px-2 py-1 text-xs">2025-05-25 17:16:32</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/1'">
            <td class="px-2 py-1 text-xs">1</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-25 16:26:24</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:09:29</td>
            <td class="px-2 py-1 text-xs">2025-05-25 17:07:20</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
        <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location.href='/task/2'">
            <td class="px-2 py-1 text-xs">2</td>
            <td class="px-2 py-1 text-xs">shell</td>
            <td class="px-2 py-1 text-xs">Test shell command</td>
            <td class="px-2 py-1 text-xs"><span class="rounded px-2 py-1 bg-blue-100 text-blue-800">running</span></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs"></td>
            <td class="px-2 py-1 text-xs">2025-05-25 16:26:24</td>
            <td class="px-2 py-1 text-xs">2025-06-09 20:10:10</td>
            <td class="px-2 py-1 text-xs">2025-05-25 17:07:20</td>
            <td class="px-2 py-1 text-xs"></td>
        </tr>
        
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>